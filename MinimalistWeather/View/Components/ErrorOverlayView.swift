//
//  ErrorOverlayView.swift
//  MinimalistWeather
//
//  Created by <PERSON><PERSON> on 2025/7/21.
//

import SwiftUI
import AutoInch

/// 錯誤處理覆蓋層組件
/// 用於顯示網路錯誤、伺服器錯誤等狀況，並提供重新載入功能
struct ErrorOverlayView: View {
    // MARK: - 屬性
    let errorMessage: String
    let onRetry: () -> Void
    let onFeedback: () -> Void
    let onChangeWeatherSource: (() -> Void)?
    let isAutoRetrying: Bool
    let retryCount: Int

    // MARK: - 初始化
    init(errorMessage: String,
         onRetry: @escaping () -> Void,
         onFeedback: @escaping () -> Void = {
             if let url = URL(string: "https://minlsm.instatus.com/") {
                 UIApplication.shared.open(url)
             }
         },
         onChangeWeatherSource: (() -> Void)? = nil,
         isAutoRetrying: Bool = false,
         retryCount: Int = 0) {
        self.errorMessage = errorMessage
        self.onRetry = onRetry
        self.onFeedback = onFeedback
        self.onChangeWeatherSource = onChangeWeatherSource
        self.isAutoRetrying = isAutoRetrying
        self.retryCount = retryCount
    }
    
    // MARK: - 視圖
    var body: some View {
        ZStack {
            // 全版面背景
            HexColor.themed(.primaryBackground)
                .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: CGFloat(20).auto()) {
                Spacer()
                
                // 錯誤圖標
                AppIconsSymbol.createView(
                    for: AppIcons.noserver, 
                    fontSize: CGFloat(80).auto(), 
                    color: HexColor.themed(.primaryText)
                )
                
                // 錯誤標題
                Text("server_error".localized)
                    .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.themed(.primaryText))
                
                // 錯誤描述
                Text("send_feedback_check_announcements".localized)
                    .font(.system(size: CGFloat(12).auto(), weight: .regular))
                    .multilineTextAlignment(.center)
                    .foregroundColor(HexColor.themed(.secondaryText))
                    .padding(.horizontal, CGFloat(40).auto())
                
                Spacer()
                
                // 按鈕區域
                VStack(spacing: CGFloat(15).auto()) {
                    // 根據重試狀態顯示不同內容
                    if isAutoRetrying {
                        // 自動重試中的狀態
                        autoRetryingView
                    } else if retryCount > 0 {
                        // 已經自動重試過，顯示手動重試按鈕和天氣來源切換按鈕
                        retryButton

                        // 如果提供了天氣來源切換回調，顯示切換按鈕
                        if onChangeWeatherSource != nil {
                            changeWeatherSourceButton
                        }

                        feedbackButton
                    } else {
                        // 第一次錯誤，不顯示按鈕（等待自動重試）
                        EmptyView()
                    }
                }
                .padding(.bottom, CGFloat(10).auto())
            }
        }
        .background(HexColor.themed(.primaryBackground))
        .zIndex(200) // 確保在所有視圖之上
    }
    
    // MARK: - 子視圖
    
    /// 重新載入按鈕
    private var retryButton: some View {
        HStack(spacing: CGFloat(10).auto()) {
            Image(systemName: "arrow.clockwise")
                .font(.system(size: CGFloat(16).auto(), weight: .medium))
                .foregroundColor(HexColor.themed(.primaryText))
            Text("retry".localized)
                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryBackground))
            // 使用系統圖標作為重新載入圖標
            Image(systemName: "arrow.clockwise")
                .font(.system(size: CGFloat(16).auto(), weight: .medium))
                .foregroundColor(HexColor.themed(.primaryBackground))
        }
        .padding(.vertical, CGFloat(12).auto())
        .frame(width: CGFloat(200).auto())
        .background(HexColor.themed(.primaryText))
        .cornerRadius(CGFloat(5).auto())
        .onTapGesture {
            onRetry()
        }
    }
    
    /// 切換天氣來源按鈕
    private var changeWeatherSourceButton: some View {
        HStack(spacing: CGFloat(10).auto()) {
            // Image(systemName: "cloud.sun")
            //     .font(.system(size: CGFloat(16).auto(), weight: .medium))
            //     .foregroundColor(HexColor.themed(.primaryBackground))
            Text("change_weather_source".localized)
                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryBackground))
        }
        .padding(.vertical, CGFloat(12).auto())
        .frame(width: CGFloat(200).auto())
        .background(HexColor.themed(.secondaryText))
        .cornerRadius(CGFloat(5).auto())
        .onTapGesture {
            onChangeWeatherSource?()
        }
    }

    /// 回饋按鈕
    private var feedbackButton: some View {
        HStack(spacing: CGFloat(10).auto()) {

            Text("status_check".localized)
                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryBackground))
        }
        .padding(.vertical, CGFloat(12).auto())
        .frame(width: CGFloat(200).auto())
        .background(HexColor.themed(.secondaryText))
        .cornerRadius(CGFloat(5).auto())
        .onTapGesture {
            onFeedback()
        }
    }

    /// 自動重試中的視圖
    private var autoRetryingView: some View {
        VStack(spacing: CGFloat(10).auto()) {
            // 載入指示器
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: HexColor.themed(.primaryText)))
                .scaleEffect(1.2)

            // 重試提示文字
            Text("auto_retrying".localized)
                .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
        }
        .padding(.vertical, CGFloat(20).auto())
    }
}

// MARK: - 預覽
#Preview {
    ErrorOverlayView(
        errorMessage: "網路連線錯誤",
        onRetry: {
            Logger.debug("重新載入")
        },
        onChangeWeatherSource: {
            Logger.debug("切換天氣來源")
        },
        isAutoRetrying: false,
        retryCount: 1
    )
}
