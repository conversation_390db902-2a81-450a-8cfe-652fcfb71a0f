//
//  WeatherContentView.swift
//  MinimalistWeather
//
//  Created by Kered on 2025/4/15.
//

import SwiftUI
import Combine
import AutoInch
import Lottie
import WidgetKit
import SwiftRater

// MARK: - 天氣內容視圖
struct WeatherContentView: View {
    // MARK: - 屬性
    @ObservedObject var viewModel: WeatherViewModel
    @EnvironmentObject private var networkMonitor: NetworkMonitor
    @ObservedObject private var iapService = IAPService.shared
    var scrollToTopAction: () -> Void
    var onNextPage: () -> Void
    @State private var showingLocation = true
    @State private var selectedTimeIndex: Int = 0 // 選中的時間索引
    @State private var hasInitializedTimeline: Bool = false // 追蹤是否已經初始化時間軸
    @State private var showTimelinePreview: Bool = false // 控制是否顯示時間軸預覽
    @State private var hideTimelineWorkItem: DispatchWorkItem? = nil // 用於存儲延遲隱藏的工作項
    @State private var isTimelinePreviewPinned: Bool = false // 追蹤預覽是否被固定
    @State private var showTemperatureChart: Bool = false // 控制是否顯示溫度圖表
    @State private var showingWeatherSourcePicker = false // 控制天氣來源選擇器顯示
    @State private var isTimelineDragging: Bool = false // 追蹤時間軸是否正在被拖動
    @State private var isLoadingVisible: Bool = true // 控制 LoadingView 的淡出效果
    @State private var isMainContentVisible: Bool = false // 控制主視圖的淡入效果
    @State private var isInitialLoad: Bool = true // 追蹤是否為首次載入
    @State private var showLocationSearch = false // 控制地區搜尋視圖顯示
    @State private var selectedLocationIndex: Int = 0 // 當前選中的城市索引
    @State private var isLocationPickerVisible: Bool = false // 控制城市選擇器的顯示
    @State private var showWeatherDetail = false // 控制天氣詳細視圖
    @State private var selectedLocationForWeatherSource: SavedLocation? // 選中的位置用於天氣來源選擇
    @State private var lastTapDate: Date? // 記錄上次點擊時間，用於雙擊檢測
    @State private var tapResetTimer: Timer? // 用於重置點擊狀態的計時器
    @State private var showingUpgradeAlert = false // 控制 IAP 升級提示顯示
    @State private var showingPaywall = false // 控制 Paywall 顯示
    @Namespace private var weatherAnimation // 用於 matchedGeometryEffect
    var horizontalPadding: CGFloat = CGFloat(60).auto() // 可調整的左右邊距
    
    // 定時器
    let timer = Timer.publish(every: 3, on: .main, in: .common).autoconnect()
    
    // MARK: - 視圖
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .top) {
                if !showWeatherDetail {
                    // 原有的主視圖
                    mainContentView(geometry: geometry)
                } else {
                    // 天氣詳細視圖
                    WeatherDetailView(
                        animation: weatherAnimation, 
                        viewModel: viewModel, 
                        selectedTimeIndex: selectedTimeIndex,
                        onDismiss: {
                            showWeatherDetail = false
                            // 當天氣詳細視圖關閉後，重新啟動計時器（如果選中的不是索引0且未固定）
                            if selectedTimeIndex != 0 && !isTimelinePreviewPinned && showTimelinePreview {
                                resetHideTimer()
                            }
                        }
                    )
                }
                
                // 載入中覆蓋層 - 確保在所有視圖之上（除了錯誤覆蓋層）
                if isLoadingVisible {
                    ZStack {
                        // 半透明背景
                        HexColor.themed(.primaryBackground)
                            .opacity(0.8)
                            .edgesIgnoringSafeArea(.all)
                        
                        // LoadingView
                        LoadingView(color: HexColor.themed(.primaryText), size: CGFloat(60).auto(), speed: 0.3)
                    }
                    .zIndex(100) // 確保在主視圖之上，但在錯誤覆蓋層之下
                    .animation(.easeOut(duration: 0.5), value: isLoadingVisible)
                }
                
                // 錯誤處理覆蓋層 - 確保在任何狀態下都能顯示
                if let errorMessage = viewModel.errorMessage {
                    ErrorOverlayView(
                        errorMessage: errorMessage,
                        onRetry: {
                            // 手動重新載入天氣資料
                            viewModel.manualRetry()
                        },
                        onChangeWeatherSource: {
                            // 設置當前位置並顯示天氣來源選擇器
                            if let currentLocation = viewModel.currentSavedLocation {
                                selectedLocationForWeatherSource = currentLocation
                                showingWeatherSourcePicker = true
                                Logger.debug("從錯誤覆蓋層觸發天氣來源選擇器: \(currentLocation.name)")
                            }
                        },
                        isAutoRetrying: viewModel.isAutoRetrying,
                        retryCount: viewModel.retryCount
                    )
                }
            }
            .background(HexColor.themed(.primaryBackground))
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .onChange(of: selectedTimeIndex) { newIndex in
            Logger.debug("時間軸索引變更為: \(newIndex), 預覽狀態: \(showTimelinePreview ? "顯示中" : "隱藏中"), 固定狀態: \(isTimelinePreviewPinned ? "已固定" : "未固定"), 拖動狀態: \(isTimelineDragging ? "拖動中" : "已釋放")")
            
            // 當切換到第一個索引（索引0）且時間軸預覽正在顯示時，且未處於拖動狀態，重設計時器
            if newIndex == 0 && showTimelinePreview && !isTimelinePreviewPinned && !isTimelineDragging {
                resetHideTimer()
            }
            
            // 同步更新溫度圖表的顯示狀態
            showTemperatureChart = showTimelinePreview
        }
        .onChange(of: viewModel.isLoading) { isLoading in
            if isLoading {
                // 當開始載入時
                withAnimation {
                    isLoadingVisible = true
                    // 首次載入或切換地區時隱藏主視圖
                    if isInitialLoad || viewModel.errorMessage != nil {
                        isMainContentVisible = false
                    } else {
                        // 非首次載入但切換地區時，也隱藏主視圖並顯示載入動畫
                        isMainContentVisible = false
                    }
                }
            } else {
                // 當載入結束時
                withAnimation {
                    isLoadingVisible = false
                }
                
                // 載入結束後，延遲顯示主視圖
                if isInitialLoad {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                        withAnimation {
                            isMainContentVisible = true
                            isInitialLoad = false // 標記首次載入已完成
                        }
                    }
                } else {
                    // 非首次載入，稍微延遲後顯示主視圖
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation {
                            isMainContentVisible = true
                        }
                    }
                }
            }
        }
        .onAppear {
            // 確保視圖初始化時狀態正確
            isLoadingVisible = viewModel.isLoading
            isMainContentVisible = !viewModel.isLoading && !isInitialLoad

            // 初始化選中的城市索引
            updateSelectedLocationIndex()

            // 添加通知觀察者，當收到通知時重置時間軸索引
            setupNotificationObservers()

            // 檢查是否顯示評分提示
            SwiftRater.check()
        }
        .onChange(of: viewModel.currentSavedLocation) { _ in
            // 當當前城市改變時，更新選中的索引
            updateSelectedLocationIndex()
        }
        .onChange(of: viewModel.savedLocations) { _ in
            // 當城市列表改變時，更新選中的索引
            updateSelectedLocationIndex()
        }
        .onChange(of: viewModel.timelinePreviewData) { _ in
            // 當時間線數據改變時，數據會自動通過 ViewModel 更新
        }
        .fullScreenCover(isPresented: $showLocationSearch) {
            LocationSearchView(viewModel: viewModel, scrollToTopAction: scrollToTopAction)
        }
        .fullScreenCover(
            isPresented: Binding(
                get: { showingWeatherSourcePicker && selectedLocationForWeatherSource != nil },
                set: { newValue in
                    showingWeatherSourcePicker = newValue
                    if !newValue {
                        selectedLocationForWeatherSource = nil
                    }
                }
            )
        ) {
            // 由於 Binding 已確保 selectedLocationForWeatherSource 不為 nil，這裡可以安全地強制解包
            let location = selectedLocationForWeatherSource!
            // Logger.success("fullScreenCover 創建 WeatherSourcePickerView for \(location.name)")

            WeatherSourcePickerView(
                selectedSource: Binding(
                    get: { location.effectiveWeatherSource },
                    set: { newSource in
                        updateLocationWeatherSource(location, newSource: newSource)
                    }
                ),
                availableSources: WeatherSource.availableSources(for: location.country),
                onDismiss: {
                    showingWeatherSourcePicker = false
                },
                onConfirm: {
                    showingWeatherSourcePicker = false
                }
            )
            .onAppear {
                let availableSources = WeatherSource.availableSources(for: location.country)
                Logger.debug("=== WeatherSourcePickerView onAppear (fullScreenCover) ===")
                Logger.debug("位置: \(location.name)")
                Logger.debug("國家代碼: '\(location.country)'")
                Logger.debug("可用來源數量: \(availableSources.count)")
                Logger.debug("可用來源: \(availableSources.map { $0.displayName })")
                Logger.debug("當前來源: \(location.effectiveWeatherSource.displayName)")
                Logger.debug("====================================")
            }
        }
        .iapUpgradeAlert(isPresented: $showingUpgradeAlert, showingPaywall: $showingPaywall)
        .fullScreenCover(isPresented: $showingPaywall) {
            PaywallView()
        }
    }
    
    // MARK: - 子視圖
    
    /// 主要內容視圖
    private func mainContentView(geometry: GeometryProxy) -> some View {
        // 中央內容部分 - 確保垂直居中
        VStack {
            Spacer()
            
            // 主要天氣內容視圖
            weatherContentView(geometry: geometry)
            
            Spacer()
        }
        .frame(width: geometry.size.width, height: geometry.size.height)
        .overlay(alignment: .bottom) {
            HStack {
                Button {
                    onNextPage()
                } label: {
                    AppIconsSymbol.createView(for: AppIcons.scrollUp, fontSize: CGFloat(44).auto(), color: HexColor.themed(.secondaryText))
                }
                Spacer() // 將按鈕推到左邊
            }
            .padding(.horizontal, horizontalPadding)
            .opacity(showTimelinePreview ? 0.0 : 1.0) // 當顯示橫向預報時淡出
        }
    }
    
    /// 主要天氣內容視圖
    private func weatherContentView(geometry: GeometryProxy) -> some View {
        // 主要內容視圖
        VStack(spacing: CGFloat(0).auto()) { // 減少間距使兩個視圖更加協調
            // 使用ZStack讓天氣圖標和橫向預報視圖疊在一起
            ZStack(alignment: .center) {
                // 天氣圖標（改為按鈕以顯示每三小時預報）
                VStack {
                    ZStack(alignment: .center) {
                        VStack(spacing: CGFloat(0).auto()) {
                            // 城市選擇器或城市名稱
                            ZStack {
                                if viewModel.savedLocations.count > 1 && iapService.isPro {
                                    // 多個城市且已訂閱 Pro 時顯示水平選擇器
                                    HorizontalLocationPicker(
                                        locations: viewModel.savedLocations,
                                        selectedIndex: $selectedLocationIndex
                                    ) { newIndex in
                                        // 切換到選中的城市
                                        if newIndex < viewModel.savedLocations.count {
                                            let selectedLocation = viewModel.savedLocations[newIndex]
                                            viewModel.useLocation(selectedLocation)
                                            // 觸發滾動到頂部，重置時間軸
                                            // scrollToTopAction()
                                        }
                                    }
                                    .frame(height: CGFloat(20).auto())
                                } else {
                                    // 單個城市、顯示時間軸預覽時，或免費使用者顯示城市名稱
                                    Text(viewModel.displayLocation.uppercased())
                                        .font(.system(size: CGFloat(20).auto(), weight: .medium, design: .rounded))
                                        .foregroundColor(HexColor.themed(.primaryText))
                                        .padding(.bottom, CGFloat(3).auto())
                                        
                                    // Text(viewModel.displayLocation.uppercased())
                                    //     .font(.system(size: showTimelinePreview ? CGFloat(16).auto() : CGFloat(20).auto(), weight: .medium, design: .rounded))
                                    //     .foregroundColor(HexColor.themed(.primaryText))
                                    //     .padding(.bottom, showTimelinePreview ? CGFloat(7).auto() : CGFloat(3).auto())
                                    //     .animation(.spring(response: 0.3), value: showTimelinePreview)
                                    //     .onTapGesture {
                                    //         // 點擊時滾動到頂部
                                    //         scrollToTopAction()
                                    //     }
                                }
                            }
                            .onLongPressGesture(minimumDuration: 0.8) {
                                // 長按作為備用方案，直接打開天氣來源選擇器
                                Logger.debug("檢測到長按，直接打開天氣來源選擇器")
                                openWeatherSourcePicker()
                            }
                            
                            HStack(spacing: CGFloat(0).auto()) {
                                // 在時間軸上方顯示日期
                                Text(getDateFromSelectedIndex() + ", ")
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                                
                                // 星期幾
                                Text(getWeekdayFromSelectedIndex().uppercased())
                                    .font(.system(size: CGFloat(14).auto(), weight: .medium))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                            .padding(.top, CGFloat(8).auto())
                            .opacity(showTimelinePreview ? 1.0 : 0.0) // 只在捲動時顯示
                            .animation(.easeInOut(duration: 0.3), value: showTimelinePreview)
                        }
                        
                    }
                    
                    // 天氣圖標 - Lottie 動畫
                    CustomLottieView(
                        animationName: getLottieAnimationName(),
                        targetColor: HexColor.themedUIColor(.primaryText),
                        isPlaying: !showTimelinePreview // 當顯示時間軸預覽時停止播放
                    )
                    .frame(width: CGFloat(200).auto(), height: CGFloat(200).auto())
                    .matchedGeometryEffect(id: "weatherIcon", in: weatherAnimation)
                    .opacity(showTimelinePreview ? 0.0 : 1.0) // 當顯示橫向預報時淡出
                    .padding(.bottom, CGFloat(0).auto())
                    .scaleEffect(1.0)
                    .onTapGesture {
                        // 只有在沒有錯誤時才允許切換詳細視圖
                        if viewModel.errorMessage == nil {
                            withAnimation {
                                showWeatherDetail.toggle()
                            }
                        }
                    }
                    
                    // 溫度顯示 - 當 API 回應 -99 時顯示故障檢修
                    if getSelectedTemperature() == "-99" {
                        ContentViewTemperatureMaintenanceView()
                            .matchedGeometryEffect(id: "temperature", in: weatherAnimation)
                            .padding(.bottom, CGFloat(30).auto())
                            .opacity(showTimelinePreview ? 0.0 : 1.0) // 當顯示橫向預報時淡出
                    } else {
                        HStack(spacing: CGFloat(2).auto()) {
                            Text("°")
                                .font(.system(size: CGFloat(30).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryBackground))
                            Text(getSelectedTemperature())
                                .font(.system(size: CGFloat(36).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))
                            Text("°")
                                .font(.system(size: CGFloat(30).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.themed(.primaryText))
                                .offset(y: CGFloat(-4).auto())
                        }
                        .matchedGeometryEffect(id: "temperature", in: weatherAnimation)
                        .padding(.bottom, CGFloat(30).auto())
                        .opacity(showTimelinePreview ? 0.0 : 1.0) // 當顯示橫向預報時淡出
                    }
                }
                
                // 橫向每小時預報滿版ScrollView（顯示五天的每三小時預報）
                ZStack {
                    TimelinePreviewScrollView(
                        timelineData: viewModel.timelinePreviewData,
                        selectedIndex: $selectedTimeIndex,
                        isVisible: $showTimelinePreview,
                        timezone: viewModel.currentSavedLocation?.timezone,
                        onUserInteraction: {
                            // 當用戶與 TimelinePreviewScrollView 互動時重置計時器
                            if !isTimelinePreviewPinned {
                                resetHideTimer()
                            }
                        },
                        onWeatherDetailOpen: {
                            // 當從 TimelinePreviewScrollView 開啟天氣詳細視圖時
                            // 取消隱藏計時器
                            hideTimelineWorkItem?.cancel()
                            withAnimation {
                                showWeatherDetail = true
                            }
                        }
                    )
                    .frame(height: CGFloat(260).auto())
                    .opacity(showTimelinePreview ? 1.0 : 0.0) // 根據狀態控制顯示
                    .animation(.easeInOut(duration: 0.3), value: showTimelinePreview)
                    .zIndex(showTimelinePreview ? 10 : 0) // 確保在前層顯示
                }
            }
            .frame(height: CGFloat(200).auto())
            
            // 使用 ZStack 和相對位置讓時間軸滿版顯示
            ZStack {
                // 支援5天天氣預報的RulerTestView
                VStack(spacing: 0) {
                    // 在時間軸上方顯示日期
                    Text(getDateFromSelectedIndex())
                        .font(.system(size: CGFloat(24).auto(), weight: .medium, design: .rounded))
                        .opacity(showTimelinePreview ? 0.0 : 0.0) // 只在捲動時顯示
                        .animation(.easeInOut(duration: 0.3), value: showTimelinePreview)
                        .padding(.bottom, CGFloat(2).auto())

                    // 星期幾
                    Text(getWeekdayFromSelectedIndex().uppercased())
                        .font(.system(size: CGFloat(12).auto(), weight: .regular))
                        .opacity(showTimelinePreview ? 0.0 : 0.0) // 只在捲動時顯示
                        .animation(.easeInOut(duration: 0.3), value: showTimelinePreview)
                        .padding(.bottom, CGFloat(-5).auto())

                    RulerTestView(
                        selectedIndex: $selectedTimeIndex,
                        forecastTimes: viewModel.forecastDateTimes,
                        timeLabels: viewModel.timeLabels,
                        timezone: viewModel.currentSavedLocation?.timezone,
                        currentTime: viewModel.currentLocationTime,
                        onDragStarted: {
                            // 開始拖動時顯示詳細預報視圖
                            isTimelineDragging = true
                            if !showTimelinePreview {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showTimelinePreview = true
                                    showTemperatureChart = true
                                }
                            }

                            // 取消現有隱藏計時器
                            hideTimelineWorkItem?.cancel()
                        },
                        onDragEnded: {
                            // 拖動結束後設置狀態並啟動計時器，但只在未固定的狀態下
                            isTimelineDragging = false
                            if !isTimelinePreviewPinned {
                                resetHideTimer()
                            }
                        }
                    )
                    .frame(height: CGFloat(85).auto())
                    .onAppear {
                        // 確保只初始化一次
                        if !hasInitializedTimeline {
                            // 確保初始選中當前時間點
                            selectedTimeIndex = 0
                            hasInitializedTimeline = true
                        }
                    }

                    // 預報模式指示器
                    // HStack {
                    //     Spacer()
                    //     Text(viewModel.forecastMode.displayName)
                    //         .font(.system(size: CGFloat(10).auto(), weight: .medium))
                    //         .foregroundColor(HexColor.themed(.secondaryText))
                    //         .padding(.horizontal, CGFloat(8).auto())
                    //         .padding(.vertical, CGFloat(2).auto())
                    //         .background(
                    //             RoundedRectangle(cornerRadius: CGFloat(8).auto())
                    //                 .fill(HexColor.themed(.primaryText).opacity(0.1))
                    //         )
                    //         .opacity(showTimelinePreview ? 0.0 : 0.7)
                    //         .animation(.easeInOut(duration: 0.3), value: showTimelinePreview)
                    // }
                    // .padding(.top, CGFloat(5).auto())
                    // .padding(.trailing, CGFloat(20).auto())
                }
            }
            .frame(height: CGFloat(110).auto()) // 調整高度以容納垂直居中的時間軸
            .scaleEffect(1.0) // 添加縮放效果的基礎
            .gesture(
                MagnificationGesture()
                    .onChanged { value in
                        // 雙指縮放手勢變化時的處理
                        Logger.debug("雙指縮放手勢變化: \(value)")
                    }
                    .onEnded { value in
                        // 雙指縮放手勢結束時的處理
                        Logger.debug("雙指縮放手勢結束: \(value)")

                        // 檢查 IAP 狀態
                        if !iapService.isPro {
                            // 非 Pro 用戶，顯示升級提示
                            showingUpgradeAlert = true
                            return
                        }

                        if value < 0.8 {
                            // 雙指縮小：切換到 current+daily 模式
                            Logger.debug("檢測到雙指縮小，切換到 current+daily 模式")
                            if viewModel.forecastMode == .hourly {
                                viewModel.toggleForecastType()
                            }
                        } else if value > 1.2 {
                            // 雙指放大：切換到 current+hourly 模式
                            Logger.debug("檢測到雙指放大，切換到 current+hourly 模式")
                            if viewModel.forecastMode == .daily {
                                viewModel.toggleForecastType()
                            }
                        }

                        // 提供觸覺反饋
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                    }
            )
            
            // 濕度和體感溫度
            ZStack {
                HStack(spacing: CGFloat(40).auto()) { // 增加間距
                    // 濕度 - 當 API 回應 -99 時顯示故障檢修
                    if getSelectedHumidity() == "-99" {
                        ContentViewMaintenanceView(icon: AppIcons.humidity)
                    } else {
                        VStack(spacing: CGFloat(0).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.humidity, fontSize: CGFloat(50).auto(), color: HexColor.themed(.primaryText))
                            HStack(spacing: CGFloat(1).auto()) {
                                Text("%")
                                    .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.primaryBackground))
                                Text(getSelectedHumidity().replacingOccurrences(of: "%", with: ""))
                                    .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                                Text("%")
                                    .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                        }
                    }

                    //降雨機率 - 當 API 回應 -99 時顯示故障檢修
                    if getSelectedPrecipitation() == "-99" {
                        ContentViewMaintenanceView(icon: AppIcons.rainp)
                    } else {
                        VStack(spacing: CGFloat(0).auto()) {
                            AppIconsSymbol.createView(for: AppIcons.rainp, fontSize: CGFloat(50).auto(), color: HexColor.themed(.primaryText))
                            HStack(spacing: CGFloat(1).auto()) {
                                Text("%")
                                    .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.primaryBackground))
                                Text(getSelectedPrecipitation().replacingOccurrences(of: "%", with: ""))
                                    .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                                Text("%")
                                    .font(.system(size: CGFloat(10).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.themed(.secondaryText))
                            }
                        }
                    }
                }
                .opacity(showTimelinePreview ? 0 : 1)
                .animation(.easeInOut(duration: 0.3), value: showTimelinePreview)
                .onTapGesture {
                    // 只有在沒有錯誤時才允許切換詳細視圖
                    if viewModel.errorMessage == nil {
                        withAnimation {
                            showWeatherDetail.toggle()
                        }
                    }
                }
                
                // 溫度變化圖表
                if !viewModel.temperatureChartData.isEmpty {
                    TemperatureChartView(
                        temperaturePoints: viewModel.temperatureChartData,
                        selectedIndex: $selectedTimeIndex,
                        timezone: viewModel.currentSavedLocation?.timezone,
                        onUserInteraction: {
                            // 當用戶與 TemperatureChartView 互動時重置計時器
                            if !isTimelinePreviewPinned {
                                resetHideTimer()
                            }
                        }
                    )
                    .opacity(showTimelinePreview ? 1 : 0)
                    .animation(.easeInOut(duration: 0.3), value: showTimelinePreview)
                    .frame(height: showTimelinePreview ? nil : 0) // 當不顯示時高度為0，避免佔用空間
                    .clipped() // 防止溢出
                }
            }
            .padding(.top, CGFloat(60).auto())
            .frame(height: CGFloat(100).auto())
        }
        .opacity(isMainContentVisible ? 1 : 0) // 控制主視圖淡入
        .animation(.easeIn(duration: 0.5).delay(0.25), value: isMainContentVisible) // 在 LoadingView 淡出後稍微延遲淡入
    }
    
    // MARK: - 通知觀察者
    
    // 設置通知觀察者
    private func setupNotificationObservers() {
        // 監聽位置更改通知
        NotificationCenter.default.addObserver(
            forName: Notification.Name("ResetTimelineIndex"),
            object: nil,
            queue: .main) { _ in
                // 重置時間軸索引到初始位置
                withAnimation {
                    selectedTimeIndex = 0 // 重置到第一個時間格
                    showTimelinePreview = false // 關閉時間軸預覽
                    showTemperatureChart = false // 關閉溫度圖表
                }
                Logger.debug("已重置時間軸索引到 0")
            }
        
        // 監聽位置資料變更通知
        NotificationCenter.default.addObserver(
            forName: Notification.Name("LocationDataChanged"),
            object: nil,
            queue: .main) { _ in
                // 重新載入儲存地區的第一筆資料
                viewModel.loadFirstSavedLocationAndRefresh()
                Logger.debug("位置資料已變更，重新載入第一筆儲存地區資料")
            }
        
        // 舊的選擇地區通知已移除，現在使用 ViewModel 的位置切換系統
        // NotificationCenter.default.addObserver(forName: Notification.Name("LoadSelectedLocation"), ...)
    }
    
    // MARK: - 輔助方法
    
    // 獲取選中時間點的溫度
    private func getSelectedTemperature() -> String {
        let temperatureValue: Int

        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            // 從選中的預覽數據中提取溫度
            let tempString = viewModel.timelinePreviewData[selectedTimeIndex].temperature
            let numberString = tempString.replacingOccurrences(of: "°C", with: "")
                                       .replacingOccurrences(of: "°F", with: "")
                                       .trimmingCharacters(in: .whitespaces)
            temperatureValue = Int(numberString) ?? 0
        } else {
            temperatureValue = Int(viewModel.temperatureNumber) ?? 0
        }

        // 檢查是否為 -99，如果是則返回特殊標記
        if temperatureValue == -99 {
            return "-99"
        }

        return "\(temperatureValue)"
    }
    
    // 獲取選中時間點的天氣圖標
    private func getSelectedWeatherIcon() -> String {
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            return AppIconsSymbol.getWeatherIconFromCode(viewModel.timelinePreviewData[selectedTimeIndex].iconCode)
        }
        return viewModel.weatherIconName
    }
    
    // 獲取對應的Lottie動畫文件名
    private func getLottieAnimationName() -> String {
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            return AppIconsSymbol.getLottieAnimationFromCode(viewModel.timelinePreviewData[selectedTimeIndex].iconCode)
        }
        return AppIconsSymbol.getLottieAnimationFromCode(viewModel.currentWeatherData?.iconCode ?? "01d")
    }
    
    // 獲取選中時間點的濕度
    private func getSelectedHumidity() -> String {
        let humidityValue: Int

        // 直接從時間軸預覽數據中獲取濕度
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            let humidityString = viewModel.timelinePreviewData[selectedTimeIndex].humidity.replacingOccurrences(of: "%", with: "")
            humidityValue = Int(humidityString) ?? 0
        } else {
            // 如果沒有預覽數據，返回當前濕度
            let humidityString = viewModel.humidityString.replacingOccurrences(of: "%", with: "")
            humidityValue = Int(humidityString) ?? 0
        }

        // 檢查是否為 -99，如果是則返回特殊標記
        if humidityValue == -99 {
            return "-99"
        }

        return "\(humidityValue)%"
    }
    
    // 獲取選中時間點的降雨機率
    private func getSelectedPrecipitation() -> String {
        let precipitationValue: Int

        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            // 直接從時間軸預覽數據中獲取降雨機率
            let precipitationString = viewModel.timelinePreviewData[selectedTimeIndex].precipitationProbability.replacingOccurrences(of: "%", with: "")
            precipitationValue = Int(precipitationString) ?? 0
        } else {
            // 直接使用 API 回應中的降雨機率，而不是從天氣預報第一筆資料取得
            precipitationValue = viewModel.currentWeatherData?.precipitationProbability ?? 0
        }

        // 檢查是否為 -99，如果是則返回特殊標記
        if precipitationValue == -99 {
            return "-99"
        }

        return "\(precipitationValue)%"
    }
    
    // 重置隱藏計時器，延長預覽顯示時間
    private func resetHideTimer() {
        // 如果預覽已固定，則不啟動隱藏計時器
        if isTimelinePreviewPinned {
            return
        }
        
        // 如果時間軸仍在拖動中，則不啟動隱藏計時器
        if isTimelineDragging {
            Logger.debug("時間軸正在拖動中，暫不啟動隱藏計時器")
            return
        }
        
        // 取消現有的隱藏計時器
        hideTimelineWorkItem?.cancel()
        
        // 建立新的延遲隱藏工作
        let workItem = DispatchWorkItem {
            withAnimation(.easeInOut(duration: 0.3)) {
                self.showTimelinePreview = false
                self.showTemperatureChart = false
            }
        }
        
        // 儲存工作項引用以便稍後可以取消
        hideTimelineWorkItem = workItem
        
        // 根據選中的索引決定延遲時間
        let delayTime: Double = selectedTimeIndex == 0 ? 0.1 : 38.5
        Logger.debug("設置時間軸預覽隱藏計時器: \(delayTime)秒, 當前選中索引: \(selectedTimeIndex), 拖動狀態: \(isTimelineDragging ? "拖動中" : "已釋放")")
        
        // 延遲執行
        DispatchQueue.main.asyncAfter(deadline: .now() + delayTime, execute: workItem)
    }
    
    // 獲取選中時間點的日期
    private func getDateFromSelectedIndex() -> String {
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            let date = viewModel.timelinePreviewData[selectedTimeIndex].date
            
            // 使用時區轉換後的日期格式
            let localDate = DateTimeFormatter.shared.convertToTimezone(date, timezoneId: viewModel.currentSavedLocation?.timezone)
            return DateTimeFormatter.shared.formatMonthDay(localDate)
        }
        return ""
    }
    
    // 獲取選中時間點的星期幾
    private func getWeekdayFromSelectedIndex() -> String {
        if !viewModel.timelinePreviewData.isEmpty && selectedTimeIndex < viewModel.timelinePreviewData.count {
            let date = viewModel.timelinePreviewData[selectedTimeIndex].date
            
            // 使用時區轉換後的星期顯示
            let localDate = DateTimeFormatter.shared.convertToTimezone(date, timezoneId: viewModel.currentSavedLocation?.timezone)
            return DateTimeFormatter.shared.formatWeekday(localDate).uppercased()
        }
        return ""
    }
    
    // 更新選中的城市索引
    private func updateSelectedLocationIndex() {
        if let currentLocation = viewModel.currentSavedLocation,
           let index = viewModel.savedLocations.firstIndex(where: { $0.id == currentLocation.id }) {
            selectedLocationIndex = index
        }
    }

    // MARK: - 長按檢測

    /// 打開天氣來源選擇器（使用 sheet 避免 fullScreenCover 衝突）
    private func openWeatherSourcePicker() {
        guard let currentLocation = viewModel.currentSavedLocation else {
            Logger.error("無法打開天氣來源選擇器：currentSavedLocation 為 nil")
            return
        }

        Logger.debug("=== 打開天氣來源選擇器 ===")
        Logger.debug("當前位置: \(currentLocation.name)")
        Logger.debug("位置ID: \(currentLocation.id)")
        Logger.debug("國家代碼: '\(currentLocation.country)'")
        Logger.debug("當前天氣來源: \(currentLocation.effectiveWeatherSource.displayName)")

        let availableSources = WeatherSource.availableSources(for: currentLocation.country)
        Logger.debug("可用來源數量: \(availableSources.count)")
        Logger.debug("可用來源: \(availableSources.map { $0.displayName })")
        Logger.debug("============================")

        // 直接設置數據並打開 sheet
        selectedLocationForWeatherSource = currentLocation
        showingWeatherSourcePicker = true
    }

    // MARK: - 天氣來源更新

    /// 更新位置天氣來源
    private func updateLocationWeatherSource(_ location: SavedLocation, newSource: WeatherSource) {
        let locationRepository = LocationRepository()

        // 更新位置的天氣來源
        locationRepository.updateLocationWeatherSource(location, newSource: newSource)

        // 重新載入 ViewModel 的位置列表（不觸發 API 呼叫）
        viewModel.loadSavedLocations(checkTimezones: false)

        // 更新 selectedLocationForWeatherSource 以反映最新狀態
        if let updatedLocation = viewModel.savedLocations.first(where: { $0.id == location.id }) {
            selectedLocationForWeatherSource = updatedLocation

            // 如果更新的是當前選中的位置，重新載入天氣資料
            if let currentLocation = viewModel.currentSavedLocation,
               currentLocation.id == location.id {
                // 使用更新後的位置重新載入天氣資料
                viewModel.useLocation(updatedLocation)
                Logger.debug("已更新當前位置 \(updatedLocation.name) 的天氣來源為 \(newSource.displayName)，重新載入天氣資料")
            } else {
                Logger.debug("已更新位置 \(updatedLocation.name) 的天氣來源為 \(newSource.displayName)")
            }
        }

        // 檢查並更新 Widget 數據
        checkAndUpdateWidgetData(for: location, newSource: newSource)
    }

    /// 檢查並更新 Widget 數據
    private func checkAndUpdateWidgetData(for location: SavedLocation, newSource: WeatherSource) {
        guard let groupDefaults = UserDefaults(suiteName: "group.com.minlsm.weather") else {
            Logger.debug("🔧 無法訪問 App Group UserDefaults")
            return
        }

        let widgetLocationId = groupDefaults.string(forKey: "widget_selected_location_id")

        // 如果 widget 選中的位置與更新的位置相同，則更新 widget 數據
        if let widgetId = widgetLocationId, widgetId == location.id.uuidString {
            Logger.debug("🔧 檢測到 Widget 位置與更新位置相同，更新 Widget 天氣來源數據")

            // 更新 App Group 中的天氣來源
            groupDefaults.set(newSource.rawValue, forKey: "widget_selected_location_weather_source")
            groupDefaults.synchronize()

            // 通知 Widget 重新載入
            WidgetCenter.shared.reloadAllTimelines()

            Logger.debug("🔧 已更新 Widget 天氣來源為: \(newSource.displayName)")
            Logger.debug("🔧 已通知 Widget 重新載入")
        } else {
            Logger.debug("🔧 Widget 位置與更新位置不同，無需更新 Widget 數據")
        }
    }
}

// MARK: - 預覽
struct WeatherContentView_Previews: PreviewProvider {
    static var previews: some View {
        WeatherContentView(viewModel: WeatherViewModel(), scrollToTopAction: {}, onNextPage: {})
    }
}

// MARK: - 水平城市選擇器
struct HorizontalLocationPicker: UIViewRepresentable {
    let locations: [SavedLocation]
    @Binding var selectedIndex: Int
    let onSelectionChanged: (Int) -> Void
    
    func makeUIView(context: Context) -> UIPickerView {
        let picker = UIPickerView()
        picker.dataSource = context.coordinator
        picker.delegate = context.coordinator
        
        // 旋轉 PickerView 使其水平
        let rotationAngle: CGFloat = -90 * (.pi/180)
        picker.transform = CGAffineTransform(rotationAngle: rotationAngle)
        
        // 完全移除背景和選擇指示器
        picker.backgroundColor = UIColor.clear
        
        // 使用更可靠的方法移除選擇指示器
        DispatchQueue.main.async {
            self.removePickerSelectionIndicator(from: picker)
        }
        
        return picker
    }
    
    // 移除選擇指示器的輔助方法
    private func removePickerSelectionIndicator(from pickerView: UIPickerView) {
        pickerView.subviews.forEach { subview in
            subview.backgroundColor = UIColor.clear
            subview.subviews.forEach { subSubview in
                subSubview.backgroundColor = UIColor.clear
                // 移除所有可能的選擇指示器視圖
                if subSubview.frame.height <= 1.0 {
                    subSubview.isHidden = true
                }
            }
        }
    }
    
    func updateUIView(_ uiView: UIPickerView, context: Context) {
        // 更新 coordinator 的 parent 引用（這是關鍵！）
        context.coordinator.parent = self
        
        // 強制重新載入所有組件的資料
        uiView.reloadAllComponents()
        
        // 確保選中的索引在有效範圍內
        if selectedIndex < locations.count {
            uiView.selectRow(selectedIndex, inComponent: 0, animated: false)
        }
        
        // 持續移除選擇指示器
        removePickerSelectionIndicator(from: uiView)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIPickerViewDataSource, UIPickerViewDelegate {
        var parent: HorizontalLocationPicker  // 改為 var 以便更新
        
        init(_ parent: HorizontalLocationPicker) {
            self.parent = parent
        }
        
        func numberOfComponents(in pickerView: UIPickerView) -> Int {
            return 1
        }
        
        func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
            return parent.locations.count
        }
        
        func pickerView(_ pickerView: UIPickerView, viewForRow row: Int, forComponent component: Int, reusing view: UIView?) -> UIView {
            let locationName = parent.locations[row].name.uppercased()
            
            // 動態計算文字寬度，避免重疊
            let font = UIFont.systemFont(ofSize: CGFloat(20).auto(), weight: .medium)
            let textSize = locationName.size(withAttributes: [.font: font])
            let containerWidth = max(textSize.width + CGFloat(40).auto(), CGFloat(120).auto()) // 最小寬度120，加40的padding
            let containerHeight: CGFloat = CGFloat(80).auto() // 增加高度以提供更多間距
            
            let containerView = UIView()
            containerView.frame = CGRect(x: 0, y: 0, width: containerWidth, height: containerHeight)
            containerView.backgroundColor = UIColor.clear
            
            let label = UILabel(frame: CGRect(x: 0, y: 0, width: containerWidth, height: containerHeight))
            label.text = locationName
            label.textAlignment = .center
            label.font = font
            label.textColor = HexColor.themedUIColor(.primaryText)
            label.backgroundColor = UIColor.clear
            
            // 添加淡入淡出效果，讓非選中項目稍微透明
            let isSelected = (row == parent.selectedIndex)
            label.alpha = isSelected ? 1.0 : 0.5
            
            // 為選中項目添加微妙的縮放效果
            if isSelected {
                label.transform = CGAffineTransform(scaleX: 1.0, y: 1.0)
            } else {
                label.transform = CGAffineTransform.identity
            }
            
            containerView.addSubview(label)
            
            // 旋轉內容使其正向顯示
            containerView.transform = CGAffineTransform(rotationAngle: 90 * (.pi/180))
            
            return containerView
        }
        
        func pickerView(_ pickerView: UIPickerView, rowHeightForComponent component: Int) -> CGFloat {
            // 動態計算最大文字寬度作為行高
            let maxWidth = parent.locations.map { location in
                let font = UIFont.systemFont(ofSize: CGFloat(20).auto(), weight: .medium)
                let textSize = location.name.uppercased().size(withAttributes: [.font: font])
                return max(textSize.width + CGFloat(40).auto(), CGFloat(120).auto())
            }.max() ?? CGFloat(120).auto()
            
            return maxWidth
        }
        
        func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
            parent.selectedIndex = row
            parent.onSelectionChanged(row)
            
            // 重新載入所有行以更新透明度和縮放效果
            DispatchQueue.main.async {
                pickerView.reloadAllComponents()
            }
        }
    }
}

// MARK: - 檢修元件

/// 濕度和降雨機率共用的檢修元件
struct ContentViewMaintenanceView: View {
    let icon: String

    var body: some View {
        VStack(spacing: CGFloat(0).auto()) {
            AppIconsSymbol.createView(for: icon, fontSize: CGFloat(50).auto(), color: HexColor.themed(.primaryText))
            Text("maintenance_status".localized)
                .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded))
                .foregroundColor(HexColor.themed(.secondaryText))
                .multilineTextAlignment(.center)
        }
    }
}

/// 溫度專用的檢修元件
struct ContentViewTemperatureMaintenanceView: View {
    var body: some View {
        VStack(spacing: CGFloat(2).auto()) {
            Text("maintenance_status".localized)
                .font(.system(size: CGFloat(20).auto(), weight: .regular, design: .rounded))
                .foregroundColor(HexColor.themed(.primaryText))
                .multilineTextAlignment(.center)
        }
    }
}
