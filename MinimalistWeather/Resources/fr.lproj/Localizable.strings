/* 
  Localizable.strings (French)
  MinimalistWeather
*/

// MARK: - Onboarding
"welcome" = "Bienvenue";
"terms_privacy" = "Conditions et confidentialité";
"privacy_policy" = "Politique de confidentialité";
"terms_of_service" = "Conditions d'utilisation";
"accept" = "Accepter";
"location" = "Lieux";
"add_forecasts_city" = "Ajouter un lieu";
"searching" = "Recherche...";
"confirm_add" = "Confirmer";
"add_more_cities" = "Ajouter d'autres lieux";
"choose_your_plan" = "Choisissez votre forfait";
"feature_plans_available" = "Les forfaits seront disponibles ici";
"coming_soon" = "Bientôt disponible...";
"continue" = "Continuer";
"all_set" = "Tout est prêt !";
"ready_to_start" = "Vous pouvez commencer à utiliser Minimalist Weather";
"get_started" = "Commencer";

// MARK: - Main App
"no_connection" = "PAS DE CONNEXION";
"check_internet_connection" = "Veuillez vérifier votre connexion internet";
"feedback" = "Avis";

// MARK: - Weather
"server_error" = "La connexion a expiré";
"send_feedback_check_announcements" = "Essayez de recharger, de changer de fournisseur ou de vérifier l'état.";
"status_check" = "Vérifier l'état";

// MARK: - Settings
"unit" = "UNITÉ";
"pro" = "Réglages Pro";
"language" = "Langue";
"time_format" = "Format de l'heure";
"12_hour_format" = "Format 12 heures";
"24_hour_format" = "Format 24 heures";

// MARK: - Location Search
"search_city_name" = "Saisir un lieu ou une adresse";
"no_locations_matched" = "AUCUN LIEU TROUVÉ";
"search_for_a_city" = "RECHERCHER UN LIEU";
"no_saved_locations" = "AUCUN LIEU ENREGISTRÉ";
"unlimited_locations_available" = "Lieux illimités disponibles.";
"upgrade_to_pro_add_more" = "Passez à Pro pour ajouter plus de lieux.";

// MARK: - Paywall
"thanks_for_pro" = "Merci d'être passé à Premium ! Vos idées nous intéressent.";
"pro_description" = "Si vous avez des questions ou suggestions, n'hésitez pas à nous en faire part.";
"unlock_full_experience" = "Déverrouillez l'expérience minimale complète";
"5_day_forecast" = "Prévisions sur 5 jours";
"multiple_locations" = "Plusieurs lieux";
"all_future_features" = "Toutes les futures fonctionnalités incluses";
"purchasing" = "Achat en cours...";
"upgrade" = "Mettre à niveau";
"restoring" = "Restauration...";
"restore" = "Restaurer";
"purchase_agreement" = "Conditions | Confidentialité";
"terms" = "Conditions";
"loading" = "CHARGEMENT";

// MARK: - Common
"ok" = "OK";
"cancel" = "Annuler";
"search" = "Rechercher";
"close" = "Fermer";
"add" = "Ajouter";
"delete" = "Supprimer";
"edit" = "Modifier";
"retry" = "Recharger";
"auto_retrying" = "Rechargement...";
"change_weather_source" = "Changer de fournisseur";

// MARK: - Time
"am" = "AM";
"pm" = "PM";
"now" = "MAINT.";

// MARK: - Weekdays
"monday" = "LUN";
"tuesday" = "MAR";
"wednesday" = "MER";
"thursday" = "JEU";
"friday" = "VEN";
"saturday" = "SAM";
"sunday" = "DIM";

// MARK: - Weather Units
"celsius" = "°C";
"fahrenheit" = "°F";
"percent" = "%";

// MARK: - Feature Plans
"current_weather_forecast" = "Prévisions météo actuelles";
"3_hour_2_day_forecast" = "Prévisions sur 48 heures";
"1_location_forecast" = "Prévisions pour 1 lieu";
"3_hour_5_day_forecast" = "Prévisions météo sur 5 jours";
"2_location_forecast" = "Prévisions pour 50 villes";
"detailed_weather_info" = "Données météo détaillées";
"custom_night_theme" = "Thème de nuit personnalisé";
"no_ads" = "Sans publicités";
"start_free" = "Commencer gratuitement";
"start_7_day_trial" = "Essai gratuit de 3 jours";
"monthly_plan" = "Mensuel";
"yearly_plan" = "Annuel -50 %";

// MARK: - Alerts
"no_connection_alert" = "Pas de connexion";
"connect_internet_message" = "Veuillez vous connecter à internet pour les mises à jour météo.";

// MARK: - IAP Upgrade
"upgrade_to_pro" = "Passer à Pro";
"upgrade_now" = "Mettre à niveau";
"upgrade_message_multiple_locations" = "Passez à Pro pour ajouter plusieurs lieux et plus de fonctionnalités.";

// MARK: - Setup Completion Alert
"please_complete_setup" = "Veuillez terminer la configuration";
"complete_setup_before_using" = "Vous devez terminer la configuration avant d'utiliser cette fonction.";
"go_to_setup" = "Aller à la configuration";

// MARK: - Subscription Period
"per_year" = "/an";
"per_month" = "/mois";

// MARK: - Settings Menu
"about" = "À PROPOS";

// MARK: - Sunrise Sunset
"sunrise" = "Lever du soleil";
"sunset" = "Coucher du soleil";
"sunset_sunrise" = "Lever et coucher du soleil";

// MARK: - Paywall Continue
"continue_using" = "Continuer l'utilisation";

// MARK: - Google Geocoding Errors
"network_error" = "Erreur de connexion réseau";
"invalid_response" = "Réponse invalide";
"api_error" = "Veuillez essayer un lieu différent ou plus spécifique";
"decoding_error" = "Impossible de lire les données";
"no_search_results" = "Aucun résultat trouvé";

// MARK: - Settings Page Titles
"temperature_unit_setting" = "Unité de température";
"time_format_setting" = "Format de l'heure";
"theme_setting" = "Réglage du thème";
"weather_source_setting" = "Fournisseur météo";

// MARK: - Theme Settings
"theme_system" = "Système";
"theme_light" = "Thème clair";
"theme_dark" = "Thème sombre";

// MARK: - Temperature Units with Symbols
"celsius_with_symbol" = "Celsius °C";
"fahrenheit_with_symbol" = "Fahrenheit °F";

// MARK: - Weather Sources
"apple_weather" = "Apple Weather";
"google_weather" = "Google Weather";
"central_weather_administration" = "Central Weather Administration";

// MARK: - Widget Settings
"widget_settings" = "Réglages du widget";

// MARK: - What's New
"whats_new" = "Nouveautés";
"show_whats_new" = "Afficher les nouveautés";
"release_notes" = "Notes de version";
"version" = "Version";
"no_updates_available" = "Aucune mise à jour disponible";

// MARK: - Purchase & Subscription Alerts
"purchase_failed" = "Échec de l'achat";
"subscription_success" = "Abonnement réussi";
"thank_you_for_subscribing" = "Merci pour votre abonnement !";
"error" = "Erreur";
"package_not_available" = "Forfait non disponible";
"cannot_get_user_information" = "Impossible d'obtenir les informations utilisateur";
"restore_failed" = "Échec de la restauration";
"restore_success" = "Restauration réussie";
"purchase_restored" = "Votre achat a été restauré";
"no_restorable_items" = "Aucun élément à restaurer";
"no_restorable_items_message" = "Nous n'avons trouvé aucun achat antérieur à restaurer.";

// MARK: - Paywall Carousel
"paywall_forecasts_120hr_title" = "Déverrouillez les prévisions complètes";
"paywall_forecasts_120hr_subtitle" = "Prévisions à long terme et données détaillées.";
"paywall_saved_50_locations_title" = "50 lieux enregistrés";
"paywall_saved_50_locations_subtitle" = "Version gratuite limitée à 1 lieu.";
"paywall_home_widget_title" = "Widget d'accueil";
"paywall_home_widget_subtitle" = "Non disponible en version gratuite.";
"paywall_night_theme_title" = "Thème sombre";
"paywall_night_theme_subtitle" = "Le thème sombre est une fonction premium.";
"paywall_switch_provider_title" = "Changer de fournisseur";
"paywall_switch_provider_subtitle" = "Non disponible en version gratuite.";

// Unknown weather
"weather_unknown" = "Météo inconnue";

// MARK: - Measurement System
"metric_system" = "Métrique";
"imperial_system" = "Impérial";
"measurement_system_setting" = "Système de mesure";

// MARK: - Wind Speed Units
"wind_speed_ms" = "m/s";
"wind_speed_mph" = "mph";

// MARK: - Distance Units
"distance_km" = "km";
"distance_mi" = "mi";

// MARK: - Precipitation Units
"precipitation_mm" = "mm";
"precipitation_in" = "in";

// MARK: - Pressure Units
"pressure_hpa" = "hPa";
"pressure_inhg" = "inHg";

// MARK: - Weather Detail Labels
"feels_like" = "Ressenti";
"humidity" = "Humidité";
"precipitation_probability" = "Précipitations";
"cloudiness" = "Nébulosité";
"uv_index" = "Indice UV";
"daily_max_uv_index" = "Indice UV Max Quotidien";
"wind_speed" = "Vitesse du vent";
"wind_gust" = "Rafales";
"visibility" = "Visibilité";
"sea_pressure" = "Pression maritime";
"ground_pressure" = "Pression au sol";
"rain_volume" = "Pluie";
"snow_volume" = "Neige";

// MARK: - Weather Providers
"weather_providers_info" = "Infos sur le fournisseur";
"more_providers_info" = "Plus d'infos fournisseur →";
"different_weather_models_info" = "Différents modèles météo offrent différents temps de prévision.";
"weather_provider_apple" = "Météo Apple";
"weather_provider_apple_subtitle" = "Prend en charge tous les pays\nPrévisions sur 120 heures";
"weather_provider_cwa" = "Central Weather Administration";
"weather_provider_cwa_subtitle" = "Uniquement pour Taïwan\nPrévisions sur 72 heures";
"weather_provider_google" = "Météo Google";
"weather_provider_google_subtitle" = "Ne prend pas en charge le Japon ou la Corée\nPrévisions sur 120 heures";
"weather_provider_openweather" = "OpenWeather";
"weather_provider_openweather_subtitle" = "Prend en charge tous les pays\nPrévisions sur 120 heures";

// MARK: - Weather Sources
"weather_source_jma" = "Japan Meteorological Agency";
"weather_source_eccc" = "Environnement et Changement climatique Canada";
"weather_source_dwd" = "Deutscher Wetterdienst";
"weather_source_nws_noaa" = "National Weather Service";
"weather_source_metoffice_ecmwf" = "The Met Office/European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_weather_com" = "Weather";
"weather_source_cwb" = "Weather Forecast Center";
"weather_source_environment_canada" = "Environnement Canada";
"weather_source_eumetnet" = "EUMETNET";
"weather_source_ecmwf" = "European Centre for Medium-range Weather Forecasts (ECMWF)";
"weather_source_noaa" = "National Oceanic and Atmospheric Administration (NOAA)";
"weather_source_metoffice" = "Met Office";
"weather_source_gem_cmc" = "GEM (CMC, Centre météorologique canadien)";

// MARK: - Weather Data Status
"maintenance_status" = "En maintenance";
