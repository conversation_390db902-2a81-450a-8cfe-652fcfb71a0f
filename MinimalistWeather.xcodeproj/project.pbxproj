// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		FC17C51F2DE419050097E856 /* RevenueCat in Frameworks */ = {isa = PBXBuildFile; productRef = FC17C51E2DE419050097E856 /* RevenueCat */; };
		FC17C5212DE419050097E856 /* RevenueCatUI in Frameworks */ = {isa = PBXBuildFile; productRef = FC17C5202DE419050097E856 /* RevenueCatUI */; };
		FC3F5FBE2E29DB2A0021C0E1 /* AutoInch in Frameworks */ = {isa = PBXBuildFile; productRef = FC3F5FBD2E29DB2A0021C0E1 /* AutoInch */; };
		FC5592862DFF05F900B9E365 /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = FC5592852DFF05F900B9E365 /* Lottie */; };
		FC909C272E33863900FCB366 /* SwiftRater in Frameworks */ = {isa = PBXBuildFile; productRef = FC909C262E33863900FCB366 /* SwiftRater */; };
		FC98682A2DD491F40018E3B0 /* AutoInch in Frameworks */ = {isa = PBXBuildFile; productRef = FC9868292DD491F40018E3B0 /* AutoInch */; };
		FCB100332E23F0BD00257AF1 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FCB100182E23ED9B00257AF1 /* WidgetKit.framework */; };
		FCB100342E23F0BD00257AF1 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FCB1001A2E23ED9B00257AF1 /* SwiftUI.framework */; };
		FCB1003F2E23F0BE00257AF1 /* WeatherWidgetsExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = FCB100322E23F0BD00257AF1 /* WeatherWidgetsExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		FCB502682E3C61AD0059F8EB /* SwiftUITooltip in Frameworks */ = {isa = PBXBuildFile; productRef = FCB502672E3C61AD0059F8EB /* SwiftUITooltip */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		FCB1003D2E23F0BE00257AF1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FC30B1832DBBDC50004E6456 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = FCB100312E23F0BD00257AF1;
			remoteInfo = WeatherWidgetsExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		FCB1002D2E23ED9C00257AF1 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				FCB1003F2E23F0BE00257AF1 /* WeatherWidgetsExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		FC17C55A2DE41B080097E856 /* StoreKitTestCertificate.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = StoreKitTestCertificate.cer; sourceTree = "<group>"; };
		FC30B1DD2DBBDF4A004E6456 /* info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = info.plist; sourceTree = "<group>"; };
		FC3BD3302DBCD711001B582C /* MinimalistWeather.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MinimalistWeather.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FC3F5FC02E29E8C60021C0E1 /* Weather Minimal.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = "Weather Minimal.storekit"; sourceTree = "<group>"; };
		FCB100182E23ED9B00257AF1 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		FCB1001A2E23ED9B00257AF1 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		FCB100322E23F0BD00257AF1 /* WeatherWidgetsExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = WeatherWidgetsExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		FCB1004F2E23FA3900257AF1 /* WeatherWidgetsExtensionRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = WeatherWidgetsExtensionRelease.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		FCB100402E23F0BE00257AF1 /* Exceptions for "WeatherWidgets" folder in "WeatherWidgetsExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = FCB100312E23F0BD00257AF1 /* WeatherWidgetsExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		FC30B1CF2DBBDCC0004E6456 /* MinimalistWeather */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MinimalistWeather;
			sourceTree = "<group>";
		};
		FCB100352E23F0BD00257AF1 /* WeatherWidgets */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				FCB100402E23F0BE00257AF1 /* Exceptions for "WeatherWidgets" folder in "WeatherWidgetsExtension" target */,
			);
			path = WeatherWidgets;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		FC30B1882DBBDC50004E6456 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FC17C51F2DE419050097E856 /* RevenueCat in Frameworks */,
				FC98682A2DD491F40018E3B0 /* AutoInch in Frameworks */,
				FC17C5212DE419050097E856 /* RevenueCatUI in Frameworks */,
				FC909C272E33863900FCB366 /* SwiftRater in Frameworks */,
				FCB502682E3C61AD0059F8EB /* SwiftUITooltip in Frameworks */,
				FC5592862DFF05F900B9E365 /* Lottie in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FCB1002F2E23F0BD00257AF1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FCB100342E23F0BD00257AF1 /* SwiftUI.framework in Frameworks */,
				FCB100332E23F0BD00257AF1 /* WidgetKit.framework in Frameworks */,
				FC3F5FBE2E29DB2A0021C0E1 /* AutoInch in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		FC30B1822DBBDC50004E6456 = {
			isa = PBXGroup;
			children = (
				FCB1004F2E23FA3900257AF1 /* WeatherWidgetsExtensionRelease.entitlements */,
				FC3F5FC02E29E8C60021C0E1 /* Weather Minimal.storekit */,
				FC17C55A2DE41B080097E856 /* StoreKitTestCertificate.cer */,
				FC30B1DD2DBBDF4A004E6456 /* info.plist */,
				FC30B1CF2DBBDCC0004E6456 /* MinimalistWeather */,
				FC3BD3302DBCD711001B582C /* MinimalistWeather.app */,
				FCB100352E23F0BD00257AF1 /* WeatherWidgets */,
				FCB100172E23ED9B00257AF1 /* Frameworks */,
				FCB100322E23F0BD00257AF1 /* WeatherWidgetsExtension.appex */,
			);
			sourceTree = "<group>";
		};
		FCB100172E23ED9B00257AF1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				FCB100182E23ED9B00257AF1 /* WidgetKit.framework */,
				FCB1001A2E23ED9B00257AF1 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		FC30B18A2DBBDC50004E6456 /* MinimalistWeather */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FC30B1962DBBDC51004E6456 /* Build configuration list for PBXNativeTarget "MinimalistWeather" */;
			buildPhases = (
				FC30B1872DBBDC50004E6456 /* Sources */,
				FC30B1882DBBDC50004E6456 /* Frameworks */,
				FC30B1892DBBDC50004E6456 /* Resources */,
				FCB1002D2E23ED9C00257AF1 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				FCB1003E2E23F0BE00257AF1 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				FC30B1CF2DBBDCC0004E6456 /* MinimalistWeather */,
			);
			name = MinimalistWeather;
			packageProductDependencies = (
				FC9868292DD491F40018E3B0 /* AutoInch */,
				FC17C51E2DE419050097E856 /* RevenueCat */,
				FC17C5202DE419050097E856 /* RevenueCatUI */,
				FC5592852DFF05F900B9E365 /* Lottie */,
				FC909C262E33863900FCB366 /* SwiftRater */,
				FCB502672E3C61AD0059F8EB /* SwiftUITooltip */,
			);
			productName = MinimalistWeather;
			productReference = FC3BD3302DBCD711001B582C /* MinimalistWeather.app */;
			productType = "com.apple.product-type.application";
		};
		FCB100312E23F0BD00257AF1 /* WeatherWidgetsExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FCB100412E23F0BE00257AF1 /* Build configuration list for PBXNativeTarget "WeatherWidgetsExtension" */;
			buildPhases = (
				FCB1002E2E23F0BD00257AF1 /* Sources */,
				FCB1002F2E23F0BD00257AF1 /* Frameworks */,
				FCB100302E23F0BD00257AF1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				FCB100352E23F0BD00257AF1 /* WeatherWidgets */,
			);
			name = WeatherWidgetsExtension;
			packageProductDependencies = (
				FC3F5FBD2E29DB2A0021C0E1 /* AutoInch */,
			);
			productName = WeatherWidgetsExtension;
			productReference = FCB100322E23F0BD00257AF1 /* WeatherWidgetsExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FC30B1832DBBDC50004E6456 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					FC30B18A2DBBDC50004E6456 = {
						CreatedOnToolsVersion = 16.3;
					};
					FCB100312E23F0BD00257AF1 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = FC30B1862DBBDC50004E6456 /* Build configuration list for PBXProject "MinimalistWeather" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hant",
				ja,
				"en-AU",
				"en-GB",
				de,
				fr,
				es,
				it,
				nl,
				da,
				sv,
				no,
				nb,
				fi,
				"fr-CA",
			);
			mainGroup = FC30B1822DBBDC50004E6456;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				FC9868282DD491F40018E3B0 /* XCRemoteSwiftPackageReference "AutoInch" */,
				FC17C51D2DE419050097E856 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */,
				FC5592842DFF05F900B9E365 /* XCRemoteSwiftPackageReference "lottie-ios" */,
				FC909C252E33862500FCB366 /* XCRemoteSwiftPackageReference "SwiftRater" */,
				FCB502662E3C61AD0059F8EB /* XCRemoteSwiftPackageReference "SwiftUI-Tooltip" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = FC30B1822DBBDC50004E6456;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FC30B18A2DBBDC50004E6456 /* MinimalistWeather */,
				FCB100312E23F0BD00257AF1 /* WeatherWidgetsExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FC30B1892DBBDC50004E6456 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FCB100302E23F0BD00257AF1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FC30B1872DBBDC50004E6456 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FCB1002E2E23F0BD00257AF1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		FCB1003E2E23F0BE00257AF1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = FCB100312E23F0BD00257AF1 /* WeatherWidgetsExtension */;
			targetProxy = FCB1003D2E23F0BE00257AF1 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		FC30B1942DBBDC51004E6456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		FC30B1952DBBDC51004E6456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FC30B1972DBBDC51004E6456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MinimalistWeather/MinimalistWeatherDebug.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 23;
				DEVELOPMENT_TEAM = BJ3PBAQ57V;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = weat;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.weather";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.minlsm.weather;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		FC30B1982DBBDC51004E6456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MinimalistWeather/MinimalistWeatherRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 23;
				DEVELOPMENT_TEAM = BJ3PBAQ57V;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = weat;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.weather";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.minlsm.weather;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		FCB100422E23F0BE00257AF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = WeatherWidgetsExtensionRelease.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 23;
				DEVELOPMENT_TEAM = BJ3PBAQ57V;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WeatherWidgets/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WeatherWidgets;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.minlsm.weather.WeatherWidgets;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		FCB100432E23F0BE00257AF1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = WeatherWidgetsExtensionRelease.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 23;
				DEVELOPMENT_TEAM = BJ3PBAQ57V;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WeatherWidgets/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WeatherWidgets;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.minlsm.weather.WeatherWidgets;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		FC30B1862DBBDC50004E6456 /* Build configuration list for PBXProject "MinimalistWeather" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FC30B1942DBBDC51004E6456 /* Debug */,
				FC30B1952DBBDC51004E6456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FC30B1962DBBDC51004E6456 /* Build configuration list for PBXNativeTarget "MinimalistWeather" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FC30B1972DBBDC51004E6456 /* Debug */,
				FC30B1982DBBDC51004E6456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FCB100412E23F0BE00257AF1 /* Build configuration list for PBXNativeTarget "WeatherWidgetsExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FCB100422E23F0BE00257AF1 /* Debug */,
				FCB100432E23F0BE00257AF1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		FC17C51D2DE419050097E856 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RevenueCat/purchases-ios-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		FC5592842DFF05F900B9E365 /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.5.2;
			};
		};
		FC909C252E33862500FCB366 /* XCRemoteSwiftPackageReference "SwiftRater" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/takecian/SwiftRater";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.2.2;
			};
		};
		FC9868282DD491F40018E3B0 /* XCRemoteSwiftPackageReference "AutoInch" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/lixiang1994/AutoInch.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.6.0;
			};
		};
		FCB502662E3C61AD0059F8EB /* XCRemoteSwiftPackageReference "SwiftUI-Tooltip" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/quassum/SwiftUI-Tooltip";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.0.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		FC17C51E2DE419050097E856 /* RevenueCat */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC17C51D2DE419050097E856 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCat;
		};
		FC17C5202DE419050097E856 /* RevenueCatUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC17C51D2DE419050097E856 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCatUI;
		};
		FC3F5FBD2E29DB2A0021C0E1 /* AutoInch */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC9868282DD491F40018E3B0 /* XCRemoteSwiftPackageReference "AutoInch" */;
			productName = AutoInch;
		};
		FC5592852DFF05F900B9E365 /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC5592842DFF05F900B9E365 /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};
		FC909C262E33863900FCB366 /* SwiftRater */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC909C252E33862500FCB366 /* XCRemoteSwiftPackageReference "SwiftRater" */;
			productName = SwiftRater;
		};
		FC9868292DD491F40018E3B0 /* AutoInch */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC9868282DD491F40018E3B0 /* XCRemoteSwiftPackageReference "AutoInch" */;
			productName = AutoInch;
		};
		FCB502672E3C61AD0059F8EB /* SwiftUITooltip */ = {
			isa = XCSwiftPackageProductDependency;
			package = FCB502662E3C61AD0059F8EB /* XCRemoteSwiftPackageReference "SwiftUI-Tooltip" */;
			productName = SwiftUITooltip;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = FC30B1832DBBDC50004E6456 /* Project object */;
}
